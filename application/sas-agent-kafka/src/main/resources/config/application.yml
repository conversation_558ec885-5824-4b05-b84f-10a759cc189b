env:
  name: ${env_name:de16}

server:
  port: 8080

spring:
  datasource:
    url: ********************************************************
    username: APP_CAMPAIGN_CDM2
    password: APP_CAMPAIGN_CDM2
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      maximum-pool-size: 10
      minimum-idle: 1

wsc:
  username: SAS_User
  password: SAS_User
  connectionTimeout: 2000
  readTimeout: 35000
  maxConnections: 50
  maxConnectionsPerClient: 50

kafka:
  ab:
    username: SAS_KAFKA_USER
    password: SAS_KAFKA_USER
    groupId: sas-agent-kafka
    apicurioRegistryUrl: "http://kafka-ui.np.ab:8080/apis/registry/v2/"
    bootstrap-servers: "kafka-01.np.ab:9092,kafka-02.np.ab:9092,kafka-03.np.ab:9092"
    topics:
      generalContract: ${env.name}.cz.airbank.ams.generalcontract.application.change.v1
      customerRelation: ${env.name}.cz.airbank.obs.generalcontract.customerrelations.change.v2
      pensionApplication: ${env.name}.cz.airbank.ams.pensionstatus.application.change.v1
      investmentApplication: ${env.name}.cz.airbank.ams.investmentsstatus.application.change.v1
      successfulDevicePairing: ${env.name}.cz.airbank.rmd.devicepairing.success.v1
      cardDigitalization: ${env.name}.cz.airbank.cms.card.digitization.new.v1
      loanApplication: ${env.name}.cz.airbank.ams.cashloan.application.status.v1
      consolidationApplication: ${env.name}.cz.airbank.ams.consolidation.application.status.v1
      overdraftApplication: ${env.name}.cz.airbank.ams.overdraft.application.status.v1
      mortgageApplication: ${env.name}.cz.airbank.ams.mortgage.application.status.v1
      mortgageRefApplication: ${env.name}.cz.airbank.ams.mortgageref.application.status.v1
      splitPaymentApplication: ${env.name}.cz.airbank.ams.splitpayment.application.status.v1
      travelInsuranceApplication: ${env.name}.cz.airbank.ams.travelinsurance.application.status.v1
      accountApplicationStatus: ${env.name}.cz.airbank.ams.account.application.status.v1
      stockEtfApplicationStatus: ${env.name}.cz.airbank.ams.stocketf.application.status.v1
      airbankClientUnityMemberPartyRemoved: ${env.name}.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartyremoved.v1
      airbankClientUnityMemberPartyDeactivated : ${env.name}.cz.airbank.o2.proxy.registration.airbankclientunitymemberpartydeactivated.v1
      airbankClientUnityMemberDeactivated: ${env.name}.cz.airbank.o2.proxy.registration.airbankclientunitymemberdeactivated.v1
      transactions: ${env.name}.cz.airbank.obs.transaction.customertransaction.v2
      loanProductStatusChange: ${env.name}.cz.airbank.obs.loan.loanstatuschange.v1
      plannedCallCreated: ${env.name}.cz.airbank.cml.planned.call.created.v1
      consent: ${env.name}.cz.airbank.cml.marketing.consents.change.v1
      sas360messageResult: ${env.name}.cz.airbank.sas.campaign.result.v1
      cardStatusChanged: ${env.name}.cz.airbank.cms.card.status.v1

airbank:
  ci360:
    url: https://extapigwservice-eu-prod.ci360.sas.com/marketingGateway/events
    token: #TODO
    application-id: SasAgentKafka
#    proxy:
#      host: app-sas-test.np.ab
#      port: 443
#      username: sascloud_user
#      password: #TODO

  http-client:
    connect-timeout: 10
    timeout: 30
  kafka:
    topics-with-long-key: ${kafka.ab.topics.generalContract},${kafka.ab.topics.customerRelation},${kafka.ab.topics.pensionApplication},${kafka.ab.topics.investmentApplication},${kafka.ab.topics.successfulDevicePairing},${kafka.ab.topics.cardDigitalization},${kafka.ab.topics.loanApplication},${kafka.ab.topics.consolidationApplication},${kafka.ab.topics.overdraftApplication},${kafka.ab.topics.mortgageApplication},${kafka.ab.topics.mortgageRefApplication},${kafka.ab.topics.splitPaymentApplication},${kafka.ab.topics.travelInsuranceApplication},${kafka.ab.topics.accountApplicationStatus},${kafka.ab.topics.stockEtfApplicationStatus},${kafka.ab.topics.loanProductStatusChange},${kafka.ab.topics.airbankClientUnityMemberDeactivated},${kafka.ab.topics.airbankClientUnityMemberPartyDeactivated},${kafka.ab.topics.airbankClientUnityMemberPartyRemoved},${kafka.ab.topics.cardStatusChanged}
    topics-with-string-key: ${kafka.ab.topics.transactions},${kafka.ab.topics.plannedCallCreated}
    consent:
      topics: ${kafka.ab.topics.consent}
    sas360messageResult:
      topics: ${kafka.ab.topics.sas360messageResult}
  database-name: APP_CAMPAIGN_CDM2
  event-settings:
    - event-name: Int_GeneralContractOwnerCustomer_Status
      topic: ${kafka.ab.topics.customerRelation}
      condition-expression: generalContractType == 'RETAIL' && relationToContract == 'OWNER'
      condition-variables: generalContractType, relationToContract
      field-mappings:
        subject_id: cuid
        GeneralContractCustomer_ContractNumber: generalContractNumber
        GeneralContractCustomer_ContractStatus: generalContractStatus
    - event-name: Int_GeneralContractOwnerEntrepreneur_Status
      topic: ${kafka.ab.topics.customerRelation}
      condition-expression: generalContractType == 'ENTREPRENEUR' && relationToContract == 'ENTITLED'
      condition-variables: generalContractType, relationToContract
      field-mappings:
        subject_id: cuid
        GeneralContractCustomer_ContractNumber: generalContractNumber
        GeneralContractCustomer_ContractStatus: generalContractStatus
    - event-name: Int_GeneralContractOwnerEntrepreneur_Application
      topic: ${kafka.ab.topics.generalContract}
      condition-expression: (applicationRequestStatus == 'UNFINISHED' || applicationRequestStatus == 'CANCELLED' || applicationRequestStatus == 'APPROVED') && (generalContractType == 'ENTREPRENEUR' || generalContractType == 'LEGAL_ENTITY')
      condition-variables: applicationRequestStatus, generalContractType
      field-mappings:
        subject_id: cuidEntitled
        GeneralContractEntrepreneur_ApplicationId: applicationId
        GeneralContractEntrepreneur_ApplicationStatus: applicationRequestStatus
        GeneralContractEntrepreneur_Email: email
    - event-name: Int_GeneralContractOwnerCustomer_Application
      topic: ${kafka.ab.topics.generalContract}
      condition-expression: (applicationRequestStatus == 'UNFINISHED' || applicationRequestStatus == 'CANCELLED' || applicationRequestStatus == 'APPROVED') && generalContractType == 'RETAIL'
      condition-variables: applicationRequestStatus, generalContractType
      field-mappings:
        subject_id: cuidEntitled
        GeneralContractCustomer_ApplicationId: applicationId
        GeneralContractCustomer_ApplicationStatus: applicationRequestStatus
        GeneralContractCustomer_Email: email
    - event-name: Int_PensionCustomer_Application
      topic: ${kafka.ab.topics.pensionApplication}
      condition-expression: applicationRequestStatus == 'DEMO' || applicationRequestStatus == 'CANCELLED' || applicationRequestStatus == 'UNFINISHED' || applicationRequestStatus == 'APPROVED' || applicationRequestStatus == 'REJECTED' || applicationRequestStatus == 'COMPLETION'
      condition-variables: applicationRequestStatus
      field-mappings:
        subject_id: cuid
        PensionCustomer_ApplicationId: applicationId
        PensionCustomer_ApplicationStatus: applicationRequestStatus
    - event-name: Int_InvestmentsCustomer_Application
      topic: ${kafka.ab.topics.investmentApplication}
      condition-expression: applicationRequestStatus == 'DEMO' || applicationRequestStatus == 'CANCELLED' || applicationRequestStatus == 'UNFINISHED' || applicationRequestStatus == 'APPROVED' || applicationRequestStatus == 'REJECTED' || applicationRequestStatus == 'COMPLETION'
      condition-variables: applicationRequestStatus
      field-mappings:
        subject_id: cuid
        Int_InvestmentsCustomer_ApplicationId: applicationId
        Int_InvestmentsCustomer_ApplicationStatus: applicationRequestStatus
    - event-name: Int_FirstDevicePairing
      topic: ${kafka.ab.topics.successfulDevicePairing}
      condition-expression: firstPairing == true
      condition-variables: firstPairing
      field-mappings:
        subject_id: cuid
        deviceName: deviceName
        deviceType: deviceType
#        os: platform
        deviceStatus: deviceStatus
        activeOperations: activeOperations
#        appVersion: appVersion
    - event-name: Int_OwnerCardNewDigitalization
      topic: ${kafka.ab.topics.cardDigitalization}
      condition-expression: cuid == ownerCuid && (state == 'APPROVED' || state == 'DONE')
      condition-variables: cuid, ownerCuid, state
      field-mappings:
        subject_id: cuid
        OwnerCardNewDigitization_digitalizationSequenceNumber: digitalizationSequenceNumber
        OwnerCardNewDigitization_state: state
    - event-name: Int_LoanCustomer_Application
      topic: ${kafka.ab.topics.loanApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        LoanCustomer_ApplicationId: applicationId
        LoanCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_ConsolidationCustomer_Application
      topic: ${kafka.ab.topics.consolidationApplication}
      condition-expression: applicationStatus == 'UNFINISHED' || applicationStatus == 'REJECTED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        ConsolidationCustomer_ApplicationId: applicationId
        ConsolidationCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_OverdraftCustomer_Application
      topic: ${kafka.ab.topics.overdraftApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        OverdraftCustomer_ApplicationId: applicationId
        OverdraftCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_MortgageCustomer_Application
      topic: ${kafka.ab.topics.mortgageApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        MortgageCustomer_ApplicationId: applicationId
        MortgageCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_MortgageRefCustomer_Application
      topic: ${kafka.ab.topics.mortgageRefApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        MortgageRefCustomer_ApplicationId: applicationId
        MortgageRefCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_SplitPaymentCustomer_Application
      topic: ${kafka.ab.topics.splitPaymentApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        SplitPaymentCustomer_ApplicationId: applicationId
        SplitPaymentCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_TravelInsuranceCustomer_Application
      topic: ${kafka.ab.topics.travelInsuranceApplication}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        TravelInsuranceCustomer_ApplicationId: applicationId
        TravelInsuranceCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_CurrentAccountCustomer_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'CURRENT_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        CurrentAccountCustomer_ApplicationId: applicationId
        CurrentAccountCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_SavingAccountCustomer_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'SAVING_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        SavingAccountCustomer_ApplicationId: applicationId
        SavingAccountCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_ChildCurrentAccountCustomer_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'CHILD_CURRENT_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        ChildCurrentAccountCustomer_ApplicationId: applicationId
        ChildCurrentAccountCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_ChildSavingAccountCustomer_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'CHILD_SAVING_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        ChildSavingAccountCustomer_ApplicationId: applicationId
        ChildSavingAccountCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_CurrentAccountEntrepreneur_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'ENTREPRENEUR_CURRENT_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        CurrentAccountEntrepreneur_ApplicationId: applicationId
        CurrentAccountEntrepreneur_ApplicationStatus: applicationStatus
    - event-name: Int_SavingAccountEntrepreneur_Application
      topic: ${kafka.ab.topics.accountApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED' && accountTypeDetail == 'ENTREPRENEUR_SAVING_ACCOUNT'
      condition-variables: applicationStatus, accountTypeDetail
      field-mappings:
        subject_id: cuid
        SavingAccountEntrepreneur_ApplicationId: applicationId
        SavingAccountEntrepreneur_ApplicationStatus: applicationStatus
    - event-name: Int_StockEtfCustomer_Application
      topic: ${kafka.ab.topics.stockEtfApplicationStatus}
      condition-expression: applicationStatus == 'UNFINISHED'
      condition-variables: applicationStatus
      field-mappings:
        subject_id: cuid
        StockEtfCustomer_ApplicationId: applicationId
        StockEtfCustomer_ApplicationStatus: applicationStatus
    - event-name: Int_SplitPaymentTransaction
      topic: ${kafka.ab.topics.transactions}
      condition-expression: splitable == true && transactionType != 'KPAYCUR' && transactionType != 'KPAYOTH'
      condition-variables: splitable, transactionType
      field-mappings:
        subject_id: ownerCuid
        SplitPaymentTransaction_TransactionId: transactionId
        SplitPaymentTransaction_Amount: amount
        SplitPaymentTransaction_FAmount: amount
        SplitPaymentTransaction_Currency: currency
        SplitPaymentTransaction_TransactionType: transactionType
        SplitPaymentTransaction_TransactionDate: bookingDate
        SplitPaymentTransaction_AccountNumber: accountNumber
    - event-name: Int_SplitPaymentCustomer
      topic: ${kafka.ab.topics.loanProductStatusChange}
      condition-expression: productType == 'SPLITPAYMENT' && (newStatus == 'ACTIVE' || newStatus == 'TERMINATED')
      condition-variables: productType, newStatus
      field-mappings:
        subject_id: cuid
        SplitPaymentCustomer_LoanNumber: loanNumber
        SplitPaymentCustomer_GeneralContractId: generalContractId
        SplitPaymentCustomer_Status: newStatus
    - event-name: Int_UnityMemberPartyDeactivated
      topic: ${kafka.ab.topics.airbankClientUnityMemberPartyDeactivated}
      field-mappings:
        subject_id: cuid
        UnityMemberPartyDeactivated_SourceParty: sourceParty
        UnityMemberPartyDeactivated_Reason: reason
        UnityMemberPartyDeactivated_ExitDate: exitDate
        UnityMemberPartyDeactivated_ExitChannel: exitChannel
        UnityMemberPartyDeactivated_ViolationDetected: violationDetected
    - event-name: Int_UnityMemberPartyRemoved
      topic: ${kafka.ab.topics.airbankClientUnityMemberPartyRemoved}
      field-mappings:
        subject_id: cuid
        UnityMemberPartyRemoved_SourceParty: sourceParty
        UnityMemberPartyRemoved_Reason: reason
        UnityMemberPartyRemoved_RemovalDate: removalDate
    - event-name: Int_UnityMemberDeactivated
      topic: ${kafka.ab.topics.airbankClientUnityMemberDeactivated}
      field-mappings:
        subject_id: cuid
        UnityMemberDeactivated_SourceParty: sourceParty
        UnityMemberDeactivated_Reason: reason
        UnityMemberDeactivated_ExitDate: exitDate
        UnityMemberDeactivated_ExitChannel: exitChannel
        UnityMemberDeactivated_ExitParty: exitParty
    - event-name: Int_PlannedCallCallback_Created
      topic: ${kafka.ab.topics.plannedCallCreated}
      condition-expression: originWay == 'CALLBACK' && cuid != null
      condition-variables: originWay, cuid
      field-mappings:
        subject_id: cuid
        PlannedCallCallback_DepartmentQueueCode: departmentQueueCode
    - event-name: CONSENT
      topic: ${kafka.ab.topics.consent}
      field-mappings:
        subject_id: cuid
    - event-name: Int_PaymentCardOwnerActivation
      topic: ${kafka.ab.topics.cardStatusChanged}
      condition-expression: cardTypeTechnology != 'VIRT' && statusAfter == 'ACTIVE' && statusBefore != 'NOT_ACTIVE' && statusBefore != 'RENEWAL'
      condition-variables: cardTypeTechnology, statusAfter, statusBefore
      field-mappings:
        subject_id: accountOwnerCuid

resilience4j.retry:
  instances:
    call360:
      maxAttempts: 2
      waitDuration: 2s

management:
  endpoints:
    web:
      exposure:
        include: [ "prometheus", "health", "info" ]