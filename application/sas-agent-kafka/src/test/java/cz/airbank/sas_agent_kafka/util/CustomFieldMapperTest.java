package cz.airbank.sas_agent_kafka.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;

import java.math.BigDecimal;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;

class CustomFieldMapperTest {

    @Test
    void mapField_withNullValue_shouldReturnNull() {
        // Act
        String result = CustomFieldMapper.mapField("anyField", null);

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void mapField_withUnknownField_shouldReturnNull() {
        // Act
        String result = CustomFieldMapper.mapField("unknownField", BigDecimal.valueOf(1000.50));

        // Assert
        assertThat(result).isNull();
    }

    @Test
    void mapField_withSplitPaymentTransactionFAmount_andBigDecimalValue_shouldReturnFormattedAmount() {
        // Arrange
        BigDecimal amount = new BigDecimal("1234.56");

        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", amount);

        // Assert
        assertThat(result).isEqualTo("1 234.56");
    }

    @ParameterizedTest
    @MethodSource("provideNumberValues")
    void mapField_withSplitPaymentTransactionFAmount_andNumberValue_shouldReturnFormattedAmount(Number value, String expected) {
        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", value);

        // Assert
        assertThat(result).isEqualTo(expected);
    }

    private static Stream<Arguments> provideNumberValues() {
        return Stream.of(
                Arguments.of(1234.56, "1 234.56"),
                Arguments.of(1000.1, "1 000.10"),
                Arguments.of(1000.0, "1 000"),
                Arguments.of(1000, "1 000"),
                Arguments.of(0, "0"),
                Arguments.of(-1234.56, "-1 234.56")
        );
    }

    @Test
    void mapField_withSplitPaymentTransactionFAmount_andStringValue_shouldReturnFormattedAmount() {
        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", "9876.54");

        // Assert
        assertThat(result).isEqualTo("9 876.54");
    }

    @Test
    void mapField_withSplitPaymentTransactionFAmount_andInvalidValue_shouldReturnStringRepresentation() {
        // Arrange
        Object invalidValue = "not-a-number";

        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", invalidValue);

        // Assert
        assertThat(result).isEqualTo(invalidValue.toString());
    }

    @Test
    void mapField_withSplitPaymentTransactionFAmount_andLargeNumber_shouldFormatCorrectly() {
        // Arrange
        BigDecimal largeAmount = new BigDecimal("1234567890.12");

        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", largeAmount);

        // Assert
        assertThat(result).isEqualTo("1 234 567 890.12");
    }

    @Test
    void mapField_withSplitPaymentTransactionFAmount_andUnsupportedType_shouldReturnStringRepresentation() {
        // Arrange
        Object unsupportedType = new Object();

        // Act
        String result = CustomFieldMapper.mapField("SplitPaymentTransaction_FAmount", unsupportedType);

        // Assert
        assertThat(result).isEqualTo(unsupportedType.toString());
    }
}